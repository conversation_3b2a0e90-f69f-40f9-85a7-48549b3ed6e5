<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>{% block title %}Taluk Office - Digital File Management System{% endblock %}</title>

    <!-- Modern CSS Framework -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom Styles -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/components.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/responsive.css') }}">

    <!-- PWA Manifest -->
    <link rel="manifest" href="{{ url_for('static', filename='manifest.json') }}">
    <meta name="theme-color" content="#2563eb">

    {% block styles %}{% endblock %}
</head>
<body>
    <!-- Government Header -->
    <header class="gov-header">
        <div class="container-fluid px-2 px-md-4">
            <div class="row align-items-center py-1 py-md-2">
                <div class="col-lg-8 col-md-7">
                    <div class="d-flex align-items-center">
                        <img src="https://upload.wikimedia.org/wikipedia/commons/5/55/Emblem_of_India.svg"
                             alt="Government of India" class="gov-emblem me-2 me-md-3" width="50" height="50">
                        <div>
                            <h1 class="gov-title mb-0">Government of Karnataka</h1>
                            <p class="gov-subtitle mb-0 d-none d-sm-block">Taluk Office - Digital File Management System</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-5 text-end">
                    <div class="gov-info d-none d-md-block">
                        <small class="text-muted">{{ 'Logged in as ' + current_user.username if current_user.is_authenticated else 'Not logged in' }}</small>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Professional Navigation -->
    <nav class="navbar navbar-expand-lg modern-nav">
        <div class="container-fluid px-2 px-md-4">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <div class="brand-icon">
                    <i class="fas fa-building"></i>
                </div>
                <div class="brand-text">
                    <strong>Taluk Office</strong>
                    <small class="d-block text-muted d-none d-md-block">File Management</small>
                </div>
            </a>

            <!-- Mobile toggle -->
            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto official-nav">
                    {% if current_user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link official-nav-link" href="{{ url_for('dashboard') }}">
                            <div class="nav-icon">
                                <i class="fas fa-home"></i>
                            </div>
                            <div class="nav-text">Dashboard</div>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link official-nav-link" href="{{ url_for('bundle_list') }}">
                            <div class="nav-icon">
                                <i class="fas fa-archive"></i>
                            </div>
                            <div class="nav-text">Bundles</div>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link official-nav-link" href="{{ url_for('scan_qrcode') }}">
                            <div class="nav-icon">
                                <i class="fas fa-qrcode"></i>
                            </div>
                            <div class="nav-text">QR Scanner</div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link official-nav-link" href="{{ url_for('qr_search_page') }}">
                            <div class="nav-icon">
                                <i class="fas fa-search-plus"></i>
                            </div>
                            <div class="nav-text">QR Search</div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link official-nav-link" href="{{ url_for('analytics') }}">
                            <div class="nav-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div class="nav-text">Reports</div>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link official-nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <div class="nav-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div class="nav-text">Administration</div>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('collaboration') }}"><i class="fas fa-users me-2"></i>User Management</a></li>
                            {% if current_user.role == 'Administrator' %}
                            <li><a class="dropdown-item" href="{{ url_for('admin_bulk_upload') }}"><i class="fas fa-file-excel me-2"></i>Bulk Excel Upload</a></li>
                            {% endif %}
                            <li><a class="dropdown-item" href="#"><i class="fas fa-database me-2"></i>Backup & Restore</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-shield-alt me-2"></i>Security Settings</a></li>
                        </ul>
                    </li>
                    {% endif %}
                </ul>

                <!-- Profile Menu -->
                <ul class="navbar-nav">
                    {% if current_user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link official-nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <div class="nav-icon">
                                <i class="fas fa-user-circle"></i>
                            </div>
                            <div class="nav-text">Profile</div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li class="dropdown-header">
                                <i class="fas fa-user me-2"></i>{{ current_user.username }}
                                <small class="d-block text-muted">{{ current_user.role }}</small>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user-cog me-2"></i>Profile Settings</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-bell me-2"></i>Notifications</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Preferences</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="btn btn-primary" href="{{ url_for('login') }}">
                            <i class="fas fa-sign-in-alt me-1"></i>Login
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
        <div class="container-fluid px-4 pt-3">
            {% for category, message in messages %}
            {% set alert_class = 'alert-success' %}
            {% set icon_class = 'fa-check-circle' %}
            {% if category == 'error' %}
                {% set alert_class = 'alert-danger' %}
                {% set icon_class = 'fa-exclamation-circle' %}
            {% elif category == 'warning' %}
                {% set alert_class = 'alert-warning' %}
                {% set icon_class = 'fa-exclamation-triangle' %}
            {% elif category == 'info' %}
                {% set alert_class = 'alert-info' %}
                {% set icon_class = 'fa-info-circle' %}
            {% endif %}
            <div class="alert {{ alert_class }} alert-dismissible fade show modern-alert" role="alert">
                <i class="fas {{ icon_class }} me-2"></i>{{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </main>

    <!-- Government Footer -->
    <footer class="gov-footer">
        <div class="container-fluid px-4">
            <div class="row">
                <div class="col-md-6">
                    <div class="footer-info">
                        <h6 class="footer-title">Taluk Office Digital System</h6>
                        <p class="footer-text">Government of Karnataka - Digital India Initiative</p>
                        <p class="footer-contact">
                            <i class="fas fa-phone me-2"></i>Helpline: 1800-XXX-XXXX<br>
                            <i class="fas fa-envelope me-2"></i>Email: <EMAIL>
                        </p>
                    </div>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="footer-links">
                        <a href="#" class="footer-link">Privacy Policy</a>
                        <a href="#" class="footer-link">Terms of Service</a>
                        <a href="#" class="footer-link">RTI</a>
                        <a href="#" class="footer-link">Contact Us</a>
                    </div>
                    <div class="footer-copyright">
                        <small>&copy; 2024 Government of Karnataka. All rights reserved.</small>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3">Processing...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>

    <!-- Custom Scripts -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('static', filename='js/voice_search.js') }}"></script>
    <script src="{{ url_for('static', filename='js/analytics.js') }}"></script>

    <!-- Service Worker for PWA -->
    <script>
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('{{ url_for("static", filename="js/service-worker.js") }}');
        }
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>