{% extends "base.html" %}

{% block title %}Bundle Management - Card Style{% endblock %}

{% block styles %}
<style>
    .compartment-management {
        padding: 20px;
        background: #f8f9fa;
        min-height: 100vh;
    }

    .page-header {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .page-title {
        color: #2c3e50;
        font-size: 28px;
        font-weight: 600;
        margin: 0;
    }

    .page-subtitle {
        color: #7f8c8d;
        margin: 5px 0 0 0;
        font-size: 16px;
    }

    .compartments-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-bottom: 30px;
        padding: 0 20px;
    }

    .compartment-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .compartment-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }

    .compartment-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 25px 20px;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .compartment-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .compartment-title {
        font-size: 26px;
        font-weight: 700;
        margin: 0;
        position: relative;
        z-index: 1;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .compartment-info {
        font-size: 15px;
        opacity: 0.95;
        margin: 8px 0 0 0;
        position: relative;
        z-index: 1;
        font-weight: 500;
    }

    .racks-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 20px;
        padding: 20px;
    }

    .rack-card {
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
    }

    .rack-card:hover {
        border-color: #3498db;
        background: #e3f2fd;
        transform: translateY(-2px);
    }

    .rack-card.expanded {
        border-color: #2980b9;
        background: #e8f4fd;
    }

    .rack-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }

    .rack-title {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
    }

    .rack-stats {
        font-size: 12px;
        color: #7f8c8d;
    }

    .rack-info {
        font-size: 14px;
        color: #34495e;
        margin-bottom: 15px;
    }

    .bundles-container {
        display: none;
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #dee2e6;
    }

    .bundles-container.show {
        display: block;
    }

    .bundles-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
        gap: 8px;
    }

    .bundle-card {
        background: linear-gradient(145deg, #ffffff, #f8f9fa);
        border: 2px solid #e9ecef;
        border-radius: 12px;
        padding: 12px 8px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        min-height: 80px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        position: relative;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }

    .bundle-card:hover {
        border-color: #667eea;
        background: linear-gradient(145deg, #f8f9ff, #e8f0ff);
        transform: translateY(-3px) scale(1.02);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
    }

    .bundle-card.has-files {
        border-color: #10b981;
        background: linear-gradient(145deg, #f0fdf4, #ecfdf5);
        box-shadow: 0 2px 8px rgba(16, 185, 129, 0.1);
    }

    .bundle-card.has-files:hover {
        border-color: #059669;
        background: linear-gradient(145deg, #ecfdf5, #d1fae5);
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.2);
    }

    .bundle-card.has-files::before {
        content: '📁';
        position: absolute;
        top: 4px;
        right: 6px;
        font-size: 12px;
        opacity: 0.7;
    }

    .bundle-number {
        font-weight: 600;
        font-size: 14px;
        color: #2c3e50;
        margin-bottom: 5px;
    }

    .bundle-file-count {
        font-size: 11px;
        color: #7f8c8d;
    }

    .bundle-card.has-files .bundle-file-count {
        color: #27ae60;
        font-weight: 500;
    }

    .bundle-qr-icon {
        position: absolute;
        top: 4px;
        left: 6px;
        font-size: 12px;
        color: #667eea;
        cursor: pointer;
        opacity: 0.7;
        transition: all 0.3s ease;
        z-index: 10;
        padding: 2px;
        border-radius: 3px;
    }

    .bundle-qr-icon:hover {
        opacity: 1;
        color: #5a67d8;
        background: rgba(102, 126, 234, 0.1);
        transform: scale(1.1);
    }

    .bundle-card.has-files .bundle-qr-icon {
        color: #10b981;
    }

    .bundle-card.has-files .bundle-qr-icon:hover {
        color: #059669;
        background: rgba(16, 185, 129, 0.1);
    }

    .stats-bar {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }

    .stat-item {
        text-align: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .stat-number {
        font-size: 24px;
        font-weight: 600;
        color: #3498db;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 14px;
        color: #7f8c8d;
    }

    .expand-icon {
        transition: transform 0.3s ease;
    }

    .rack-card.expanded .expand-icon {
        transform: rotate(180deg);
    }

    /* QR Modal Styles */
    .qr-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .qr-modal.show {
        opacity: 1;
        visibility: visible;
    }

    .qr-modal-content {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        max-width: 400px;
        width: 90%;
        max-height: 90vh;
        overflow-y: auto;
        transform: scale(0.7);
        transition: transform 0.3s ease;
    }

    .qr-modal.show .qr-modal-content {
        transform: scale(1);
    }

    .qr-modal-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 15px 15px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .qr-modal-header h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
    }

    .qr-close-btn {
        background: none;
        border: none;
        color: white;
        font-size: 24px;
        cursor: pointer;
        padding: 0;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: background 0.3s ease;
    }

    .qr-close-btn:hover {
        background: rgba(255, 255, 255, 0.2);
    }

    .qr-modal-body {
        padding: 20px;
    }

    .qr-info {
        margin-bottom: 20px;
        background: #f8f9fa;
        padding: 15px;
        border-radius: 10px;
    }

    .qr-info-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
    }

    .qr-info-item:last-child {
        margin-bottom: 0;
    }

    .qr-label {
        font-weight: 500;
        color: #7f8c8d;
    }

    .qr-value {
        font-weight: 600;
        color: #2c3e50;
    }

    .qr-code-container {
        text-align: center;
        margin-bottom: 20px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 10px;
    }

    .qr-code-image {
        max-width: 200px;
        width: 100%;
        height: auto;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        background: white;
        padding: 10px;
    }

    .qr-actions {
        display: flex;
        gap: 10px;
        justify-content: center;
    }

    .qr-actions .btn {
        padding: 8px 16px;
        border-radius: 6px;
        text-decoration: none;
        font-size: 14px;
        font-weight: 500;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .qr-actions .btn-primary {
        background: #667eea;
        color: white;
    }

    .qr-actions .btn-primary:hover {
        background: #5a67d8;
        transform: translateY(-1px);
    }

    .qr-actions .btn-success {
        background: #10b981;
        color: white;
    }

    .qr-actions .btn-success:hover {
        background: #059669;
        transform: translateY(-1px);
    }

    @media (max-width: 1200px) {
        .compartments-container {
            grid-template-columns: 1fr;
            gap: 25px;
            padding: 0 15px;
        }
    }

    @media (max-width: 768px) {
        .compartments-container {
            padding: 0 10px;
            gap: 20px;
        }

        .racks-grid {
            grid-template-columns: 1fr;
            gap: 15px;
            padding: 15px;
        }

        .bundles-grid {
            grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
            gap: 6px;
        }

        .compartment-header {
            padding: 20px 15px;
        }

        .compartment-title {
            font-size: 1.5rem;
        }

        .compartment-info {
            font-size: 14px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="compartment-management">
    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-warehouse me-3"></i>Bundle Management System
        </h1>
        <p class="page-subtitle">Organize and manage files across compartments, racks, and bundles</p>
    </div>

    <!-- Statistics Bar -->
    <div class="stats-bar">
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-number">2</div>
                <div class="stat-label">Compartments</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">20</div>
                <div class="stat-label">Total Racks</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">800</div>
                <div class="stat-label">Total Bundles</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ total_files or 0 }}</div>
                <div class="stat-label">Total Files</div>
            </div>
        </div>
    </div>

    <!-- Compartments Container -->
    <div class="compartments-container">
        <!-- Compartment 1: Racks 1-10 -->
        <div class="compartment-card">
            <div class="compartment-header">
                <h2 class="compartment-title">
                    <i class="fas fa-building me-2"></i>Compartment 1
                </h2>
                <p class="compartment-info">Racks 1-10 • Bundles 1-400</p>
            </div>
            
            <div class="racks-grid">
                {% for rack_num in range(1, 11) %}
                {% set start_bundle = (rack_num - 1) * 40 + 1 %}
                {% set end_bundle = rack_num * 40 %}
                <div class="rack-card" data-rack="{{ rack_num }}" onclick="toggleRack({{ rack_num }})">
                    <div class="rack-header">
                        <h3 class="rack-title">
                            <i class="fas fa-archive me-2"></i>Rack {{ rack_num }}
                        </h3>
                        <i class="fas fa-chevron-down expand-icon"></i>
                    </div>
                    <div class="rack-info">
                        Bundles {{ start_bundle }}-{{ end_bundle }}
                    </div>
                    <div class="rack-stats">
                        40 bundles • {{ rack_files.get(rack_num, 0) }} files
                    </div>
                    
                    <div class="bundles-container" id="bundles-{{ rack_num }}">
                        <div class="bundles-grid">
                            {% for bundle_num in range(start_bundle, end_bundle + 1) %}
                            {% set file_count = bundle_files.get(bundle_num, 0) %}
                            <div class="bundle-card {% if file_count > 0 %}has-files{% endif %}"
                                 data-bundle="{{ bundle_num }}"
                                 onclick="viewBundle({{ bundle_num }}, event)">
                                <div class="bundle-number">{{ bundle_num }}</div>
                                <div class="bundle-file-count">
                                    {% if file_count > 0 %}
                                        {{ file_count }} files
                                    {% else %}
                                        Empty
                                    {% endif %}
                                </div>
                                <div class="bundle-qr-icon" onclick="showBundleQR({{ bundle_num }}, event)" title="View QR Code">
                                    <i class="fas fa-qrcode"></i>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Compartment 2: Racks 11-20 -->
        <div class="compartment-card">
            <div class="compartment-header">
                <h2 class="compartment-title">
                    <i class="fas fa-building me-2"></i>Compartment 2
                </h2>
                <p class="compartment-info">Racks 11-20 • Bundles 401-800</p>
            </div>
            
            <div class="racks-grid">
                {% for rack_num in range(11, 21) %}
                {% set start_bundle = (rack_num - 1) * 40 + 1 %}
                {% set end_bundle = rack_num * 40 %}
                <div class="rack-card" data-rack="{{ rack_num }}" onclick="toggleRack({{ rack_num }})">
                    <div class="rack-header">
                        <h3 class="rack-title">
                            <i class="fas fa-archive me-2"></i>Rack {{ rack_num }}
                        </h3>
                        <i class="fas fa-chevron-down expand-icon"></i>
                    </div>
                    <div class="rack-info">
                        Bundles {{ start_bundle }}-{{ end_bundle }}
                    </div>
                    <div class="rack-stats">
                        40 bundles • {{ rack_files.get(rack_num, 0) }} files
                    </div>
                    
                    <div class="bundles-container" id="bundles-{{ rack_num }}">
                        <div class="bundles-grid">
                            {% for bundle_num in range(start_bundle, end_bundle + 1) %}
                            {% set file_count = bundle_files.get(bundle_num, 0) %}
                            <div class="bundle-card {% if file_count > 0 %}has-files{% endif %}"
                                 data-bundle="{{ bundle_num }}"
                                 onclick="viewBundle({{ bundle_num }}, event)">
                                <div class="bundle-number">{{ bundle_num }}</div>
                                <div class="bundle-file-count">
                                    {% if file_count > 0 %}
                                        {{ file_count }} files
                                    {% else %}
                                        Empty
                                    {% endif %}
                                </div>
                                <div class="bundle-qr-icon" onclick="showBundleQR({{ bundle_num }}, event)" title="View QR Code">
                                    <i class="fas fa-qrcode"></i>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function toggleRack(rackNum) {
    const rackCard = document.querySelector(`[data-rack="${rackNum}"]`);
    const bundlesContainer = document.getElementById(`bundles-${rackNum}`);

    rackCard.classList.toggle('expanded');
    bundlesContainer.classList.toggle('show');
}

function viewBundle(bundleNum, event) {
    if (event) {
        event.stopPropagation();
    }

    // Determine compartment based on bundle number
    const compartmentNum = bundleNum <= 400 ? 1 : 2;

    // Check if bundle has files
    const bundleCard = document.querySelector(`[data-bundle="${bundleNum}"]`);
    if (bundleCard && bundleCard.classList.contains('has-files')) {
        // Redirect to bundle detail page with village-wise organization
        window.location.href = `/bundle/${bundleNum}`;
    } else {
        // Show bundle detail page even if empty
        window.location.href = `/bundle/${bundleNum}`;
    }
}

function showBundleQR(bundleNum, event) {
    if (event) {
        event.stopPropagation();
    }

    // Calculate bundle information
    const compartmentNum = bundleNum <= 400 ? 1 : 2;
    const rackNum = Math.floor((bundleNum - 1) / 40) + 1;

    // Create QR modal
    const modal = document.createElement('div');
    modal.className = 'qr-modal';
    modal.innerHTML = `
        <div class="qr-modal-content">
            <div class="qr-modal-header">
                <h3><i class="fas fa-qrcode me-2"></i>Bundle ${bundleNum} QR Code</h3>
                <button class="qr-close-btn" onclick="closeQRModal()">&times;</button>
            </div>
            <div class="qr-modal-body">
                <div class="qr-info">
                    <div class="qr-info-item">
                        <span class="qr-label">Bundle:</span>
                        <span class="qr-value">${bundleNum}</span>
                    </div>
                    <div class="qr-info-item">
                        <span class="qr-label">Compartment:</span>
                        <span class="qr-value">${compartmentNum}</span>
                    </div>
                    <div class="qr-info-item">
                        <span class="qr-label">Rack:</span>
                        <span class="qr-value">${rackNum}</span>
                    </div>
                </div>
                <div class="qr-code-container">
                    <img src="/static/qrcodes/bundle_${bundleNum}_qr.png"
                         alt="Bundle ${bundleNum} QR Code"
                         class="qr-code-image"
                         onerror="this.src='/static/images/qr-placeholder.svg'; this.alt='QR Code not generated yet';"
                         onload="this.parentElement.querySelector('.qr-generate-btn')?.remove();">
                    <div class="qr-generate-btn" style="margin-top: 10px;">
                        <button onclick="generateBundleQR(${bundleNum})" class="btn btn-warning btn-sm">
                            <i class="fas fa-magic me-1"></i>Generate QR Code
                        </button>
                    </div>
                </div>
                <div class="qr-actions">
                    <a href="/static/qrcodes/bundle_${bundleNum}_qr.png"
                       download="bundle_${bundleNum}_qr.png"
                       class="btn btn-primary btn-sm">
                        <i class="fas fa-download me-1"></i>Download QR
                    </a>
                    <button onclick="openBundleSearch(${bundleNum})" class="btn btn-success btn-sm">
                        <i class="fas fa-search me-1"></i>Open Search
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Show modal with animation
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
}

function closeQRModal() {
    const modal = document.querySelector('.qr-modal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

function openBundleSearch(bundleNum) {
    window.open(`/bundle-search-interface/${bundleNum}`, '_blank');
}

function generateBundleQR(bundleNum) {
    const generateBtn = document.querySelector('.qr-generate-btn button');
    if (generateBtn) {
        generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Generating...';
        generateBtn.disabled = true;
    }

    // Call API to generate QR code
    fetch(`/api/generate-bundle-qr/${bundleNum}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Reload the QR image
            const qrImage = document.querySelector('.qr-code-image');
            if (qrImage) {
                qrImage.src = `/static/qrcodes/bundle_${bundleNum}_qr.png?t=${Date.now()}`;
                qrImage.alt = `Bundle ${bundleNum} QR Code`;
            }

            // Remove generate button
            const generateBtnContainer = document.querySelector('.qr-generate-btn');
            if (generateBtnContainer) {
                generateBtnContainer.remove();
            }
        } else {
            alert('Failed to generate QR code: ' + (data.error || 'Unknown error'));
            if (generateBtn) {
                generateBtn.innerHTML = '<i class="fas fa-magic me-1"></i>Generate QR Code';
                generateBtn.disabled = false;
            }
        }
    })
    .catch(error => {
        console.error('Error generating QR code:', error);
        alert('Failed to generate QR code. Please try again.');
        if (generateBtn) {
            generateBtn.innerHTML = '<i class="fas fa-magic me-1"></i>Generate QR Code';
            generateBtn.disabled = false;
        }
    });
}

// Keep all racks collapsed by default - no auto-expansion
document.addEventListener('DOMContentLoaded', function() {
    // All racks start collapsed - no automatic expansion
    console.log('Bundle management loaded - all racks collapsed by default');
});
</script>
{% endblock %}
