{% extends "base.html" %}

{% block title %}Dashboard - Taluk Office{% endblock %}

{% block styles %}
<style>
.dashboard-header {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid var(--gray-200);
}

.welcome-text {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
}

.welcome-subtitle {
    color: var(--gray-600);
    font-size: 1.1rem;
}

/* Minimal Official Dashboard Navigation */
.dashboard-navbar {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.navbar-header {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    color: white;
    padding: 12px 24px;
    font-weight: 600;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    border-radius: 8px 8px 0 0;
}

.quick-actions {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: wrap;
    padding: 16px 24px;
    gap: 12px;
    background: #f9fafb;
    border-radius: 0 0 8px 8px;
}

.quick-action-btn {
    background: #ffffff;
    color: #374151;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: 500;
    font-size: 14px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    white-space: nowrap;
    transition: all 0.2s ease;
    min-height: 36px;
}

.quick-action-btn:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
    color: #111827;
    text-decoration: none;
}

.quick-action-btn.primary {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.quick-action-btn.primary:hover {
    background: #2563eb;
    border-color: #2563eb;
    color: white;
}

.quick-action-btn i {
    font-size: 14px;
}

/* Dropdown styling */
.dropdown-toggle::after {
    margin-left: 6px;
}

.dropdown-menu {
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 4px 0;
    margin-top: 4px;
    min-width: 200px;
}

.dropdown-item {
    padding: 8px 16px;
    font-size: 14px;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.15s ease;
}

.dropdown-item:hover {
    background: #f3f4f6;
    color: #111827;
}

.dropdown-item i {
    font-size: 14px;
    width: 16px;
}

/* Action groups */
.action-group {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0 8px;
    border-right: 1px solid #e5e7eb;
}

.action-group:last-child {
    border-right: none;
}

/* Responsive design */
@media (max-width: 768px) {
    .quick-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
        padding: 16px;
    }

    .action-group {
        border-right: none;
        border-bottom: 1px solid #e5e7eb;
        padding: 0 0 8px 0;
        margin-bottom: 8px;
    }

    .action-group:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .quick-action-btn {
        width: 100%;
        justify-content: center;
    }
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.recent-files {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.section-header {
    background: var(--gradient-primary);
    color: white;
    padding: 1.5rem 2rem;
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.files-list {
    padding: 0;
    margin: 0;
    list-style: none;
}

.file-item {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--gray-200);
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: between;
}

.file-item:hover {
    background: var(--gray-50);
}

.file-item:last-child {
    border-bottom: none;
}

.file-info {
    flex: 1;
}

.file-name {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.25rem;
    font-size: 1.1rem;
}

.file-meta {
    color: var(--gray-600);
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.file-actions {
    display: flex;
    gap: 0.5rem;
}

.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--gray-500);
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.activity-feed {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    margin-top: 2rem;
}

.activity-item {
    padding: 1rem 2rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.activity-icon.created {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.activity-icon.viewed {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
}

.activity-icon.scanned {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.activity-content {
    flex: 1;
}

.activity-text {
    margin: 0;
    color: var(--gray-800);
    font-weight: 500;
}

.activity-time {
    color: var(--gray-500);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-top: 2rem;
}

@media (max-width: 992px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .navbar-header {
        font-size: 1rem;
        padding: 0.8rem 1.5rem;
    }

    .quick-actions {
        padding: 0.8rem;
        gap: 0.4rem;
    }

    .quick-action-btn {
        font-size: 0.85rem;
        padding: 0.5rem 1rem;
    }
}

@media (max-width: 576px) {
    .dashboard-header {
        padding: 1.5rem;
        text-align: center;
    }

    .welcome-text {
        font-size: 1.5rem;
    }

    .welcome-subtitle {
        font-size: 1rem;
    }

    .navbar-header {
        padding: 0.8rem 1rem;
        font-size: 0.95rem;
    }

    .quick-actions {
        flex-direction: column;
        padding: 1rem;
        gap: 0.5rem;
    }

    .quick-action-btn {
        width: 100%;
        justify-content: center;
        padding: 0.8rem 1rem;
    }

    .dropdown-menu {
        width: 100%;
        position: static !important;
        transform: none !important;
        margin-top: 0.5rem;
        border-radius: var(--radius-md);
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-4">
    <!-- Dashboard Header -->
    <div class="dashboard-header">
        <div class="row align-items-center">
            <div class="col-12">
                <h1 class="welcome-text">Welcome, {{ current_user.username }}</h1>
                <p class="welcome-subtitle">Taluk Office Digital File Management Dashboard</p>
            </div>
        </div>
    </div>

    <!-- Dashboard Navigation Bar -->
    <div class="dashboard-navbar">
        <div class="navbar-header">
            <i class="fas fa-tachometer-alt"></i>
            Dashboard Actions
        </div>
        <div class="quick-actions">
            <!-- Primary Actions Group -->
            <div class="action-group">
                <a href="{{ url_for('add_file') }}" class="quick-action-btn primary">
                    <i class="fas fa-file-plus"></i>Add Document
                </a>
                <a href="{{ url_for('upload_excel') }}" class="quick-action-btn primary">
                    <i class="fas fa-file-excel"></i>Upload Excel
                </a>
                <a href="{{ url_for('scan_qrcode') }}" class="quick-action-btn">
                    <i class="fas fa-qrcode"></i>Scan QR
                </a>
            </div>

            <!-- Management Group -->
            <div class="action-group">
                <div class="dropdown">
                    <button class="quick-action-btn dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-archive"></i>Compartments
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ url_for('compartment_qr_management') }}">
                            <i class="fas fa-qrcode"></i>QR Management
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('view_compartment_qr', compartment_number=1) }}">
                            <i class="fas fa-archive"></i>Compartment 1
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('view_compartment_qr', compartment_number=2) }}">
                            <i class="fas fa-archive"></i>Compartment 2
                        </a></li>
                    </ul>
                </div>

                <a href="{{ url_for('bundle_list') }}" class="quick-action-btn">
                    <i class="fas fa-layer-group"></i>Bundles
                </a>
            </div>

            <!-- Navigation Group -->
            <div class="action-group">
                <div class="dropdown">
                    <button class="quick-action-btn dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-search"></i>Browse
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ url_for('compartment_bundles', compartment_number=1) }}">
                            <i class="fas fa-archive"></i>Compartment 1 (1-400)
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('compartment_bundles', compartment_number=2) }}">
                            <i class="fas fa-archive"></i>Compartment 2 (401-800)
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('global_search') }}">
                            <i class="fas fa-search"></i>Search Files
                        </a></li>
                    </ul>
                </div>

                <a href="{{ url_for('analytics') if url_for('analytics') else '#' }}" class="quick-action-btn">
                    <i class="fas fa-chart-bar"></i>Reports
                </a>
            </div>

            <!-- System Group -->
            <div class="action-group">
                <div class="dropdown">
                    <button class="quick-action-btn dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-cog"></i>System
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ url_for('collaboration') if url_for('collaboration') else '#' }}">
                            <i class="fas fa-users"></i>Users
                        </a></li>
                        <li><a class="dropdown-item" href="#">
                            <i class="fas fa-download"></i>Export
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#">
                            <i class="fas fa-cog"></i>Settings
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Grid -->
    <div class="stats-grid">
        <div class="stats-card">
            <div class="stats-icon primary">
                <i class="fas fa-file-alt"></i>
            </div>
            <div class="stats-number">{{ stats.total_files }}</div>
            <div class="stats-label">Total Documents</div>
        </div>

        <div class="stats-card">
            <div class="stats-icon success">
                <i class="fas fa-eye"></i>
            </div>
            <div class="stats-number">{{ stats.processed_files }}</div>
            <div class="stats-label">Documents Processed</div>
        </div>

        <div class="stats-card">
            <div class="stats-icon warning">
                <i class="fas fa-qrcode"></i>
            </div>
            <div class="stats-number">{{ stats.qr_codes_generated }}</div>
            <div class="stats-label">QR Codes Generated</div>
        </div>

        <div class="stats-card">
            <div class="stats-icon info">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stats-number">{{ moment().strftime('%H:%M') if moment else 'Active' }}</div>
            <div class="stats-label">System Status</div>
        </div>
    </div>

    <!-- Dashboard Grid -->
    <div class="dashboard-grid">
        <!-- Recent Files -->
        <div class="recent-files">
            <h2 class="section-header">
                <i class="fas fa-file-alt me-2"></i>Recent Files
            </h2>

            {% if files %}
            <ul class="files-list">
                {% for file in files[:5] %}
                <li class="file-item">
                    <div class="file-info">
                        <div class="file-name">{{ file.title }}</div>
                        <div class="file-meta">
                            <span><i class="fas fa-map-marker-alt me-1"></i>Rack {{ file.location.rack_number }}, Row {{ file.location.row_number }}</span>
                            <span><i class="fas fa-calendar me-1"></i>{{ file.created_at.strftime('%Y-%m-%d') }}</span>
                        </div>
                    </div>
                    <div class="file-actions">
                        <a href="{{ url_for('view_file', file_id=file.id) }}" class="action-btn view" title="View File">
                            <i class="fas fa-eye"></i>
                        </a>
                        <button class="action-btn download" title="Download QR Code">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </li>
                {% endfor %}
            </ul>
            {% else %}
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-folder-open"></i>
                </div>
                <h4>No files yet</h4>
                <p>Start by adding your first file to the system.</p>
                <a href="{{ url_for('add_file') }}" class="btn btn-primary-modern">
                    <i class="fas fa-plus me-2"></i>Add Your First File
                </a>
            </div>
            {% endif %}
        </div>

        <!-- Activity Feed -->
        <div class="activity-feed">
            <h2 class="section-header">
                <i class="fas fa-activity me-2"></i>Recent Activity
            </h2>

            <div class="activity-item">
                <div class="activity-icon created">
                    <i class="fas fa-plus"></i>
                </div>
                <div class="activity-content">
                    <p class="activity-text">File "Project Report.pdf" was created</p>
                    <div class="activity-time">2 hours ago</div>
                </div>
            </div>

            <div class="activity-item">
                <div class="activity-icon viewed">
                    <i class="fas fa-eye"></i>
                </div>
                <div class="activity-content">
                    <p class="activity-text">File "Meeting Notes.docx" was viewed</p>
                    <div class="activity-time">4 hours ago</div>
                </div>
            </div>

            <div class="activity-item">
                <div class="activity-icon scanned">
                    <i class="fas fa-qrcode"></i>
                </div>
                <div class="activity-content">
                    <p class="activity-text">QR code scanned for "Budget 2024.xlsx"</p>
                    <div class="activity-time">6 hours ago</div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Add real-time updates using Socket.IO
const socket = io();

socket.on('file_activity', function(data) {
    // Add new activity to the feed
    const activityFeed = document.querySelector('.activity-feed');
    const newActivity = document.createElement('div');
    newActivity.className = 'activity-item';
    newActivity.innerHTML = `
        <div class="activity-icon ${data.action}">
            <i class="fas fa-${getActionIcon(data.action)}"></i>
        </div>
        <div class="activity-content">
            <p class="activity-text">${data.user} ${data.action} "${data.file_title}"</p>
            <div class="activity-time">Just now</div>
        </div>
    `;

    // Insert at the beginning
    const firstActivity = activityFeed.querySelector('.activity-item');
    if (firstActivity) {
        firstActivity.parentNode.insertBefore(newActivity, firstActivity);
    }

    // Remove last item if more than 5
    const activities = activityFeed.querySelectorAll('.activity-item');
    if (activities.length > 5) {
        activities[activities.length - 1].remove();
    }
});

function getActionIcon(action) {
    const icons = {
        'created': 'plus',
        'viewed': 'eye',
        'scanned': 'qrcode',
        'downloaded': 'download'
    };
    return icons[action] || 'file';
}

// Add smooth animations
document.addEventListener('DOMContentLoaded', function() {
    // Animate stats cards
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Animate file items
    const fileItems = document.querySelectorAll('.file-item');
    fileItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateX(-20px)';

        setTimeout(() => {
            item.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateX(0)';
        }, 500 + (index * 100));
    });
});
</script>
{% endblock %}
