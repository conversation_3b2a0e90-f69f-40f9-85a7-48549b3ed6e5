{% extends "base.html" %}

{% block title %}Compartment QR Codes - Taluk Office{% endblock %}

{% block styles %}
<style>
.compartment-management {
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
}

.page-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    margin-bottom: 40px;
    text-align: center;
    border: 1px solid #e9ecef;
}

.page-title {
    color: #2c3e50;
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 15px 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.page-subtitle {
    color: #6c757d;
    font-size: 1.2rem;
    margin: 0 0 25px 0;
    line-height: 1.5;
}

.compartment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}

.compartment-card {
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 25px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    border: 2px solid #e9ecef;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.compartment-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #007bff, #0056b3);
    border-radius: 25px 25px 0 0;
}

.compartment-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
    border-color: #007bff;
}

.compartment-card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    padding: 30px 35px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    overflow: hidden;
}

.compartment-card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
}

.compartment-card:hover .compartment-card-header::before {
    right: 100%;
}

.compartment-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.compartment-status {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    padding: 10px 18px;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.compartment-card-body {
    padding: 35px;
    background: white;
}

.qr-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 35px;
    align-items: start;
    margin-bottom: 35px;
}

.qr-preview {
    text-align: center;
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    padding: 25px;
    border-radius: 20px;
    border: 2px dashed #dee2e6;
    transition: all 0.3s ease;
}

.qr-preview:hover {
    border-color: #007bff;
    background: linear-gradient(145deg, #e3f2fd, #bbdefb);
}

.qr-image {
    max-width: 200px;
    height: 200px;
    border: 4px solid #fff;
    border-radius: 20px;
    padding: 15px;
    background: white;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    object-fit: contain;
    transition: all 0.3s ease;
}

.qr-image:hover {
    transform: scale(1.05);
    box-shadow: 0 12px 35px rgba(0,0,0,0.2);
}

.qr-placeholder {
    width: 200px;
    height: 200px;
    background: linear-gradient(135deg, #f1f3f4, #e8eaed);
    border: 4px solid #fff;
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 1rem;
    font-weight: 600;
    margin: 0 auto;
    transition: all 0.3s ease;
}

.qr-placeholder:hover {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    color: #007bff;
}

.qr-placeholder i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.7;
}

.qr-info {
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    padding: 25px;
    border-radius: 20px;
    border: 1px solid #e9ecef;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
}

.qr-info h4 {
    color: #2c3e50;
    font-size: 1.3rem;
    font-weight: 700;
    margin: 0 0 20px 0;
    display: flex;
    align-items: center;
    gap: 10px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.compartment-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 25px;
}

.info-item {
    text-align: center;
    padding: 20px 15px;
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    border-radius: 15px;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.info-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #007bff, #0056b3);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.info-item:hover::before {
    transform: scaleX(1);
}

.info-item:hover {
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.info-label {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 8px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
}

.compartment-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 25px;
}

.compartment-actions .btn {
    padding: 14px 24px;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
    cursor: pointer;
    font-size: 0.9rem;
    position: relative;
    overflow: hidden;
}

.compartment-actions .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.compartment-actions .btn:hover::before {
    left: 100%;
}

.compartment-actions .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.generation-info {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border: 2px solid #2196f3;
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 40px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(33, 150, 243, 0.1);
}

.generation-info h4 {
    color: #1976d2;
    margin-bottom: 20px;
    font-size: 1.5rem;
    font-weight: 700;
}

.generation-info p {
    margin: 0 0 25px 0;
    color: #424242;
    font-size: 1.1rem;
    line-height: 1.6;
}

.cli-command {
    background: linear-gradient(135deg, #263238 0%, #37474f 100%);
    color: #4fc3f7;
    padding: 25px;
    border-radius: 15px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    overflow-x: auto;
    text-align: left;
    border: 1px solid #37474f;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.2);
    line-height: 1.6;
}

.empty-state {
    text-align: center;
    padding: 80px 40px;
    color: #6c757d;
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    border-radius: 25px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

.empty-icon {
    font-size: 5rem;
    margin-bottom: 25px;
    opacity: 0.6;
    color: #bdbdbd;
}

.empty-state h4 {
    color: #2c3e50;
    font-size: 1.8rem;
    margin-bottom: 15px;
    font-weight: 600;
}

.empty-state p {
    color: #6c757d;
    font-size: 1.1rem;
    margin: 0;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .compartment-grid {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .qr-section {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .compartment-info {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .compartment-actions {
        flex-direction: column;
        gap: 10px;
    }

    .compartment-actions .btn {
        width: 100%;
        justify-content: center;
    }

    .page-header {
        padding: 25px;
    }

    .page-title {
        font-size: 2rem;
    }

    .compartment-card-body {
        padding: 25px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="compartment-management">
    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-qrcode me-3"></i>Compartment QR Management
        </h1>
        <p class="page-subtitle">Generate and manage QR codes for physical file compartments</p>
        <div style="margin-top: 20px;">
            <a href="{{ url_for('dashboard') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
            <a href="{{ url_for('bundle_list') }}" class="btn btn-primary ms-2">
                <i class="fas fa-archive me-2"></i>View Bundles
            </a>
        </div>
    </div>

    {% if not compartment_qrs %}
    <!-- Generation Info -->
    <div class="generation-info">
        <h4><i class="fas fa-info-circle me-2"></i>Generate Compartment QR Codes</h4>
        <p>No compartment QR codes have been generated yet. Use the Flask CLI command to generate static QR codes for compartments:</p>
        <div class="cli-command">
            flask generate-compartment-qrcodes
        </div>
    </div>

    <!-- Empty State -->
    <div class="empty-state">
        <div class="empty-icon">
            <i class="fas fa-qrcode"></i>
        </div>
        <h4>No Compartment QR Codes</h4>
        <p>Generate compartment QR codes using the CLI command above to get started.</p>
    </div>
    {% else %}
    <!-- Compartment QR Codes Grid -->
    <div class="compartment-grid">
        {% for qr in compartment_qrs %}
        <div class="compartment-card">
            <div class="compartment-card-header">
                <h3 class="compartment-title">
                    <i class="fas fa-archive me-2"></i>Compartment {{ qr.compartment_number }}
                </h3>
                <span class="compartment-status">
                    {% if qr.is_active %}Active{% else %}Inactive{% endif %}
                </span>
            </div>
            
            <div class="compartment-card-body">
                <!-- QR Section -->
                <div class="qr-section">
                    <div class="qr-preview">
                        {% if qr.qr_image_path and qr.qr_image_path != 'static/qrcodes/compartment_' + qr.compartment_number|string + '_qr.png' %}
                            <img src="{{ url_for('get_compartment_qr_image', compartment_number=qr.compartment_number) }}"
                                 alt="Compartment {{ qr.compartment_number }} QR Code"
                                 class="qr-image"
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="qr-placeholder" style="display: none;">
                                <i class="fas fa-qrcode"></i>
                                <span>QR Code</span>
                                <small>Not Generated</small>
                            </div>
                        {% else %}
                            <div class="qr-placeholder">
                                <i class="fas fa-qrcode"></i>
                                <span>QR Code</span>
                                <small>Click Generate</small>
                            </div>
                        {% endif %}
                    </div>

                    <div class="qr-info">
                        <h4><i class="fas fa-info-circle"></i>Compartment Details</h4>
                        <div class="compartment-info">
                            <div class="info-item">
                                <div class="info-label">Bundle Range</div>
                                <div class="info-value">{{ qr.bundle_range_start }}-{{ qr.bundle_range_end }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Total Bundles</div>
                                <div class="info-value">{{ (qr.bundle_range_end - qr.bundle_range_start + 1) }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Rack Range</div>
                                <div class="info-value">
                                    {% if qr.compartment_number == 1 %}1-10{% else %}11-20{% endif %}
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Status</div>
                                <div class="info-value" style="color: {% if qr.is_active %}#27ae60{% else %}#e74c3c{% endif %};">
                                    {% if qr.is_active %}Active{% else %}Inactive{% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Actions -->
                <div class="compartment-actions">
                    <a href="{{ url_for('view_compartment_qr', compartment_number=qr.compartment_number) }}"
                       class="btn btn-primary">
                        <i class="fas fa-eye me-2"></i>View Details
                    </a>
                    <a href="{{ url_for('compartment_bundles', compartment_number=qr.compartment_number) }}"
                       class="btn btn-success">
                        <i class="fas fa-archive me-2"></i>View Bundles
                    </a>
                    <button onclick="generateQR({{ qr.compartment_number }})"
                            class="btn btn-warning">
                        <i class="fas fa-qrcode me-2"></i>Generate QR
                    </button>
                    <a href="{{ url_for('download_compartment_qr', compartment_number=qr.compartment_number) }}"
                       class="btn btn-outline-primary">
                        <i class="fas fa-download me-2"></i>Download
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Additional Info -->
    <div class="generation-info">
        <h4><i class="fas fa-terminal me-2"></i>CLI Commands</h4>
        <p>Use these Flask CLI commands to manage compartment QR codes:</p>
        <div class="cli-command">
            # List all compartment QR codes<br>
            flask list-compartment-qrcodes<br><br>
            # Regenerate a specific compartment QR code<br>
            flask regenerate-compartment-qr &lt;compartment_number&gt;
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animate compartment cards
    const cards = document.querySelectorAll('.compartment-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 200);
    });
});

function generateQR(compartmentNumber) {
    const button = event.target;
    const originalText = button.innerHTML;

    // Show loading state
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating...';
    button.disabled = true;

    // Make AJAX request to generate QR
    fetch(`/api/generate-compartment-qr/${compartmentNumber}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Reload the page to show the new QR code
            location.reload();
        } else {
            alert('Failed to generate QR code: ' + (data.error || 'Unknown error'));
            button.innerHTML = originalText;
            button.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to generate QR code. Please try again.');
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// Add click handlers for compartment cards
document.querySelectorAll('.compartment-card').forEach(card => {
    card.addEventListener('click', function(e) {
        // Don't trigger if clicking on buttons
        if (e.target.closest('.compartment-actions')) {
            return;
        }

        // Get compartment number from the card
        const compartmentNumber = this.querySelector('.compartment-title').textContent.match(/\d+/)[0];
        window.location.href = `/compartment-qr/${compartmentNumber}`;
    });
});
</script>
{% endblock %}
