{% extends "base.html" %}

{% block title %}Taluk Office - Digital File Management System{% endblock %}

{% block content %}
<div class="landing-page">
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <h1 class="hero-title">
                            <span class="text-primary">Digital File Management</span><br>
                            for Government Offices
                        </h1>
                        <p class="hero-description">
                            Streamline your document management with our comprehensive digital solution. 
                            Organize, search, and access government files efficiently with QR code technology 
                            and advanced bundle management.
                        </p>
                        <div class="hero-actions">
                            {% if current_user.is_authenticated %}
                            <a href="{{ url_for('dashboard') }}" class="btn btn-primary btn-lg me-3">
                                <i class="fas fa-tachometer-alt me-2"></i>Go to Dashboard
                            </a>
                            <a href="{{ url_for('global_search') }}" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-search me-2"></i>Search Files
                            </a>
                            {% else %}
                            <a href="{{ url_for('login') }}" class="btn btn-primary btn-lg me-3">
                                <i class="fas fa-sign-in-alt me-2"></i>Login to System
                            </a>
                            <a href="#features" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-info-circle me-2"></i>Learn More
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-image">
                        <div class="government-emblem">
                            <img src="https://upload.wikimedia.org/wikipedia/commons/5/55/Emblem_of_India.svg" 
                                 alt="Government of India" class="emblem-large">
                            <div class="emblem-text">
                                <h3>Government of Karnataka</h3>
                                <p>Digital India Initiative</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features-section">
        <div class="container">
            <div class="section-header text-center">
                <h2>Key Features</h2>
                <p>Comprehensive digital file management for government offices</p>
            </div>
            
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-qrcode"></i>
                        </div>
                        <h4>QR Code Management</h4>
                        <p>Generate and scan QR codes for instant file access and location tracking.</p>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-archive"></i>
                        </div>
                        <h4>Bundle Organization</h4>
                        <p>Organize files into bundles with rack-based storage system for easy retrieval.</p>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-file-excel"></i>
                        </div>
                        <h4>Excel Integration</h4>
                        <p>Bulk upload and manage files through Excel imports with automatic organization.</p>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <h4>Advanced Search</h4>
                        <p>Search files by village, survey number, reference ID, or any document attribute.</p>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h4>Secure Access</h4>
                        <p>Role-based access control with comprehensive audit trails and security features.</p>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h4>Analytics & Reports</h4>
                        <p>Generate detailed reports and analytics for file usage and system performance.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    {% if current_user.is_authenticated %}
    <section class="stats-section">
        <div class="container">
            <div class="section-header text-center">
                <h2>System Overview</h2>
                <p>Current system statistics and usage</p>
            </div>
            
            <div class="row">
                <div class="col-md-3 mb-4">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="stat-number">{{ total_files or 0 }}</div>
                        <div class="stat-label">Total Files</div>
                    </div>
                </div>
                
                <div class="col-md-3 mb-4">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-archive"></i>
                        </div>
                        <div class="stat-number">{{ total_bundles or 0 }}</div>
                        <div class="stat-label">Bundles</div>
                    </div>
                </div>
                
                <div class="col-md-3 mb-4">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="stat-number">{{ total_villages or 0 }}</div>
                        <div class="stat-label">Villages</div>
                    </div>
                </div>
                
                <div class="col-md-3 mb-4">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-qrcode"></i>
                        </div>
                        <div class="stat-number">{{ total_qr_codes or 0 }}</div>
                        <div class="stat-label">QR Codes</div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    {% endif %}

    <!-- Call to Action -->
    <section class="cta-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h2>Ready to Get Started?</h2>
                    <p>Experience efficient digital file management for your government office.</p>
                    {% if current_user.is_authenticated %}
                    <a href="{{ url_for('dashboard') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-arrow-right me-2"></i>Access Dashboard
                    </a>
                    {% else %}
                    <a href="{{ url_for('login') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>Login to Continue
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.landing-page {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.hero-section {
    padding: 4rem 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.hero-description {
    font-size: 1.2rem;
    color: #6c757d;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.government-emblem {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.emblem-large {
    width: 120px;
    height: 120px;
    margin-bottom: 1rem;
}

.emblem-text h3 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.emblem-text p {
    color: #6c757d;
    margin: 0;
}

.features-section {
    padding: 4rem 0;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.feature-card {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    height: 100%;
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

.stats-section {
    padding: 4rem 0;
    background: white;
}

.stat-card {
    text-align: center;
    padding: 2rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    border-left: 4px solid #007bff;
}

.stat-icon {
    font-size: 2.5rem;
    color: #007bff;
    margin-bottom: 1rem;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    color: #6c757d;
    font-weight: 500;
}

.cta-section {
    padding: 4rem 0;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.cta-section h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.cta-section p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}
</style>
{% endblock %}
